package mocks

import (
	"context"
	"errors"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
)

// MockQueries implements database.Querier interface for testing
type MockQueries struct {
	database.Querier          // Embed the interface to get default implementations
	ValidateDeviceDetailsFunc func(ctx context.Context, arg database.ValidateDeviceDetailsParams) ([]database.ValidateDeviceDetailsRow, error)
	GetDeviceDataFunc         func(ctx context.Context, id int64) (database.GetDeviceDataRow, error)
	CreateAttendeeFunc        func(ctx context.Context, arg database.CreateAttendeeParams) error
}

func NewMockQueries() *MockQueries {
	return &MockQueries{
		ValidateDeviceDetailsFunc: func(ctx context.Context, arg database.ValidateDeviceDetailsParams) ([]database.ValidateDeviceDetailsRow, error) {
			// Default implementation - valid device
			if arg.ID == 1 && arg.BranchID == 1 {
				return []database.ValidateDeviceDetailsRow{
					{
						ID:         1,
						DeviceName: "Test Device",
						Location:   "Test Location",
						IsOnline:   true,
						IsBan:      false,
						BranchName: "Test Branch",
					},
				}, nil
			}
			return []database.ValidateDeviceDetailsRow{}, nil
		},
		GetDeviceDataFunc: func(ctx context.Context, id int64) (database.GetDeviceDataRow, error) {
			// Default implementation - return test device data
			if id == 1 {
				return database.GetDeviceDataRow{
					ID:         1,
					DeviceName: "Test Door Lock",
					BranchID:   1,
				}, nil
			}
			return database.GetDeviceDataRow{}, errors.New("device not found")
		},
		CreateAttendeeFunc: func(ctx context.Context, arg database.CreateAttendeeParams) error {
			// Default implementation - success
			return nil
		},
	}
}

func (m *MockQueries) ValidateDeviceDetails(ctx context.Context, arg database.ValidateDeviceDetailsParams) ([]database.ValidateDeviceDetailsRow, error) {
	if m.ValidateDeviceDetailsFunc != nil {
		return m.ValidateDeviceDetailsFunc(ctx, arg)
	}
	return []database.ValidateDeviceDetailsRow{}, errors.New("not implemented")
}

func (m *MockQueries) GetDeviceData(ctx context.Context, id int64) (database.GetDeviceDataRow, error) {
	if m.GetDeviceDataFunc != nil {
		return m.GetDeviceDataFunc(ctx, id)
	}
	return database.GetDeviceDataRow{}, errors.New("not implemented")
}

func (m *MockQueries) CreateAttendee(ctx context.Context, arg database.CreateAttendeeParams) error {
	if m.CreateAttendeeFunc != nil {
		return m.CreateAttendeeFunc(ctx, arg)
	}
	return errors.New("not implemented")
}

// SetValidDevice configures the mock to return a valid device for the given parameters
func (m *MockQueries) SetValidDevice(deviceID, branchID int64, deviceName string) {
	m.ValidateDeviceDetailsFunc = func(ctx context.Context, arg database.ValidateDeviceDetailsParams) ([]database.ValidateDeviceDetailsRow, error) {
		if arg.ID == deviceID && arg.BranchID == branchID {
			return []database.ValidateDeviceDetailsRow{
				{
					ID:         deviceID,
					DeviceName: deviceName,
					Location:   "Test Location",
					IsOnline:   true,
					IsBan:      false,
					BranchName: "Test Branch",
				},
			}, nil
		}
		return []database.ValidateDeviceDetailsRow{}, nil
	}

	m.GetDeviceDataFunc = func(ctx context.Context, id int64) (database.GetDeviceDataRow, error) {
		if id == deviceID {
			return database.GetDeviceDataRow{
				ID:         deviceID,
				DeviceName: deviceName,
				BranchID:   branchID,
			}, nil
		}
		return database.GetDeviceDataRow{}, errors.New("device not found")
	}
}

// SetDatabaseError configures the mock to return database errors
func (m *MockQueries) SetDatabaseError(err error) {
	m.ValidateDeviceDetailsFunc = func(ctx context.Context, arg database.ValidateDeviceDetailsParams) ([]database.ValidateDeviceDetailsRow, error) {
		return nil, err
	}
	m.GetDeviceDataFunc = func(ctx context.Context, id int64) (database.GetDeviceDataRow, error) {
		return database.GetDeviceDataRow{}, err
	}
}
